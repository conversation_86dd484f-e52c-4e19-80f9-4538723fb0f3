<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg"
    android:orientation="vertical"
    android:paddingTop="44dp">

    <!--顶部导航栏-->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:elevation="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <com.score.callmetest.ui.widget.AlphaImageView
                android:id="@+id/iv_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_centerVertical="true"
                android:src="@drawable/nav_btn_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="About Us"
                android:textColor="@android:color/black"
                android:textSize="18sp"
                android:textStyle="bold" />
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- 内容区整体包裹，设置背景色 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 顶部与内容区之间的图片装饰条 -->
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="33dp"
            android:src="@drawable/logo" />
        <!-- 版本号-->
        <TextView
            android:id="@+id/tv_ver"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="23dp"
            android:paddingVertical="2dp"
            android:text="@string/version"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="0dp">

                <!-- privacy Policy -->
                <com.score.callmetest.ui.widget.AlphaLinearLayout
                    android:id="@+id/item_privacy_policy"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="@android:color/white"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Privacy Policy"
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right" />
                </com.score.callmetest.ui.widget.AlphaLinearLayout>
                <!-- 分割线 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:background="@color/divide_line" />

                <!-- User Agreement -->
                <com.score.callmetest.ui.widget.AlphaLinearLayout
                    android:id="@+id/item_user_agreement"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="@android:color/white"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="User Agreement"
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right" />
                </com.score.callmetest.ui.widget.AlphaLinearLayout>
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
</LinearLayout>