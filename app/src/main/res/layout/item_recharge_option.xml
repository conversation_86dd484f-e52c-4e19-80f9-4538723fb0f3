<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@android:color/white">
    <!-- 倒计时动画容器 - 只在有倒计时时显示 -->
    <FrameLayout
        android:id="@+id/frame_countdowm"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:visibility="gone">

        <!-- SVGA倒计时背景动画 -->
        <com.score.callmetest.ui.widget.AlphaSVGAImageView
            android:id="@+id/svga_countdown"
            android:layout_width="125dp"
            android:layout_height="27dp"
            android:layout_gravity="center_horizontal"
            android:scaleType="fitCenter"/>

        <!-- 倒计时文字 - 精确定位在SVGA动画中心 -->
        <TextView
            android:id="@+id/tv_countdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|center_horizontal"
            android:layout_marginTop="4dp"
            android:text="01:22:33"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:textStyle="bold"
            android:fontFamily="@font/roboto_medium"
            android:includeFontPadding="false"
            android:gravity="center"/>

    </FrameLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- 背景动画容器 -->
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/bg_animation_image"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:layout_marginTop="8dp"
            android:scaleType="fitCenter"
            android:src="@drawable/gift_light_bg"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="62dp"
            android:layout_height="62dp"
            android:layout_marginTop="12dp"
            android:src="@drawable/coin"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <!-- 促销活动背景动画容器 -->
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/bg_animation_image_1"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginTop="30dp"
            android:scaleType="fitCenter"
            android:src="@drawable/gift_light_bg"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_icon_1"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:layout_marginTop="35dp"
            android:src="@drawable/coin"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- HOT标签 - 确保在iv_icon上面 -->
        <TextView
            android:id="@+id/tv_hot_red"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:background="@drawable/bg_item_recharge_hot"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="HOT"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            android:visibility="visible"
            android:elevation="2dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/line_coin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="85dp"
            android:gravity="center_horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_coin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="100"
                android:textAlignment="center"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                android:fontFamily="@font/roboto_medium"/>

            <ImageView
                android:id="@+id/image_coin"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginStart="2dp"
                android:layout_gravity="center"
                android:src="@drawable/coin" />

            <TextView
                android:id="@+id/tv_bonus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:layout_gravity="center"
                android:text="+50%"
                android:textAlignment="center"
                android:textColor="@color/red_coin_store"
                android:textSize="14sp"
                android:fontFamily="@font/roboto_medium" />

        </LinearLayout>


        <com.score.callmetest.ui.widget.AlphaLinearLayout
            android:id="@+id/price_layout"
            android:layout_width="match_parent"
            android:layout_height="49dp"
            android:layout_marginTop="12dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/line_coin"
            app:layout_constraintVertical_bias="0.0">

            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_regular"
                android:text="$9.99"
                android:textColor="#000"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_old_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:fontFamily="@font/roboto_regular"
                android:paddingStart="2dp"
                android:text="$19.99"
                android:textColor="#888"
                android:textSize="12sp" />
        </com.score.callmetest.ui.widget.AlphaLinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
