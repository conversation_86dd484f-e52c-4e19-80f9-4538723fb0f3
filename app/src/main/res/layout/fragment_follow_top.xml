<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg"
    tools:context=".ui.mine.follow.FollowFragment">

<!--
    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:background="@mipmap/main_bg"
        app:layout_constraintBottom_toTopOf="@id/view_pager"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1.0" />-->
    <!-- 返回按钮 -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/btn_return"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="15dp"
        android:scaleType="fitCenter"
        android:src="@drawable/nav_btn_back"
        app:layout_constraintBottom_toBottomOf="@id/tab_follow"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/tab_follow" />

    <!--  tab  -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_follow"
        style="@style/CustomTabStyle"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:layout_marginTop="44dp"
        android:background="@android:color/transparent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tabGravity="center"
        app:tabIndicatorHeight="0dp"
        app:tabMode="fixed" />

    <!-- ViewPager2用于承载内容 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintTop_toBottomOf="@id/tab_follow"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>