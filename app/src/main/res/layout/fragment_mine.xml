<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never"
    android:background="#F5F5FA">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="189dp"
            android:background="@drawable/main_bg" />

        <!-- 右上角按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btn_edit"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="53dp"
            android:layout_marginEnd="25dp"
            android:src="@drawable/edit" />

        <!-- 头像 -->
        <com.score.callmetest.ui.widget.CircleIconButton
            android:id="@+id/iv_avatar"
            android:layout_width="68dp"
            android:layout_height="68dp"
            android:layout_marginStart="15dp"
            android:layout_marginTop="68dp"
            app:bgColor="@android:color/white"
            app:iconSrc="@drawable/placeholder" />


        <!-- 用户名和装饰 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/iv_avatar"
            android:layout_alignBottom="@+id/iv_avatar"
            android:layout_marginStart="8dp"
            android:layout_toEndOf="@id/iv_avatar"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_nickname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="CCHUBBY"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:fontFamily="@font/roboto_bold"/>
            <TextView
                android:id="@+id/tv_vip_explanation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="You are not a VIP yet"
                android:textColor="#646464"
                android:textSize="13sp"
                android:fontFamily="@font/roboto_regular"
                android:visibility="gone"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/line_fans_and_card"
            android:layout_width="match_parent"
            android:layout_height="78dp"
            android:layout_below="@+id/iv_avatar"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:orientation="horizontal">


            <!-- 关注/粉丝卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/fans_card"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="match_parent"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <com.score.callmetest.ui.widget.AlphaLinearLayout
                        android:id="@+id/following_card"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_following"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_medium"
                            android:includeFontPadding="false"
                            android:text="0"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:includeFontPadding="false"
                            android:text="Following"
                            android:textColor="#838282"
                            android:textSize="13sp" />
                    </com.score.callmetest.ui.widget.AlphaLinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="32dp"
                        android:background="#EEEEEE" />

                    <com.score.callmetest.ui.widget.AlphaLinearLayout
                        android:id="@+id/follower_card"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_followers"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:includeFontPadding="false"
                            android:text="0"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            android:fontFamily="@font/roboto_medium"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:includeFontPadding="false"
                            android:text="Followers"
                            android:textColor="#838282"
                            android:textSize="13sp" />
                    </com.score.callmetest.ui.widget.AlphaLinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/my_card"
                android:layout_marginStart="9dp"
                android:layout_width="112dp"
                android:layout_height="78dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center_horizontal">

                    <ImageView
                        android:id="@+id/iv_my_card"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginTop="6dp"
                        android:src="@drawable/card" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAlignment="center"
                            android:includeFontPadding="false"
                            android:text="My cards: "
                            android:textColor="@color/black"
                            android:fontFamily="@font/roboto_medium"
                            android:textStyle="bold"
                            android:textSize="13sp" />


                        <TextView
                            android:id="@+id/tv_card_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:includeFontPadding="false"
                            android:textAlignment="center"
                            android:text="3"
                            android:textColor="#FE712C"
                            android:fontFamily="@font/roboto_medium"
                            android:textStyle="bold"
                            android:textSize="13sp" />
                    </LinearLayout>
                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- 功能列表 -->
        <LinearLayout
            android:id="@+id/line_function"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/line_fans_and_card"
            android:layout_marginHorizontal="13dp"
            android:layout_marginTop="13dp"
            android:orientation="vertical"
            android:background="@drawable/bg_while_rounded">

            <!-- vip列表-->
            <com.score.callmetest.ui.widget.AlphaRelativeLayout
                android:id="@+id/line_vip"
                android:layout_width="match_parent"
                android:layout_height="66dp"
                android:layout_below="@+id/line_fans_and_card"
                android:layout_marginHorizontal="12dp"
                android:layout_marginTop="13dp"
                android:visibility="gone"
                android:background="@drawable/btn_vip">

                <ImageView
                    android:layout_width="19dp"
                    android:layout_height="19dp"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    android:src="@drawable/vip_crown" />

                <TextView
                    android:id="@+id/tv_vip_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="43dp"
                    android:paddingBottom="2dp"
                    android:text="MY VIP"
                    android:textColor="#552712"
                    android:fontFamily="@font/roboto_bold"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:typeface="sans" />

                <TextView
                    android:id="@+id/tv_vip_explanation1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="47dp"
                    android:gravity="end"
                    android:text="You are not a VIP yet"
                    android:textColor="#502717 "
                    android:textSize="15sp"
                    android:textStyle="bold" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="25dp"
                    android:src="@drawable/enter" />
            </com.score.callmetest.ui.widget.AlphaRelativeLayout>

            <!--金币商店-->
            <com.score.callmetest.ui.widget.AlphaRelativeLayout
                android:id="@+id/line_coin_store"
                android:layout_width="match_parent"
                android:layout_height="66dp"
                android:layout_below="@+id/line_fans_and_card"
                android:layout_marginHorizontal="12dp"
                android:layout_marginTop="10dp"
                android:background="#FFF5D9">

                <ImageView
                    android:layout_width="19dp"
                    android:layout_height="19dp"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    android:src="@drawable/coin" />

                <TextView
                    android:id="@+id/tv_my_coins"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="43dp"
                    android:paddingBottom="2dp"
                    android:text="My Coins"
                    android:textColor="@color/black"
                    android:fontFamily="@font/roboto_medium"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:typeface="sans" />

                <TextView
                    android:id="@+id/tv_coin_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="40dp"
                    android:gravity="end"
                    android:text="200"
                    android:textColor="#FF712C"
                    android:textSize="15sp"
                    android:textStyle="bold" />

                <ImageView
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="25dp"
                    android:src="@drawable/triangle" />
            </com.score.callmetest.ui.widget.AlphaRelativeLayout>

            <com.score.callmetest.ui.widget.MineFunctionItemView
                android:id="@+id/language"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                app:mfi_icon="@drawable/language"
                android:visibility="gone"
                app:mfi_title="Language" />

            <com.score.callmetest.ui.widget.MineFunctionItemView
                android:id="@+id/customer_service"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                app:mfi_icon="@drawable/customer"
                app:mfi_title="Customer Service" />

            <com.score.callmetest.ui.widget.MineFunctionItemView
                android:id="@+id/settings"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                app:mfi_icon="@drawable/settings"
                app:mfi_title="Settings" />
        </LinearLayout>
    </RelativeLayout>
</ScrollView> 