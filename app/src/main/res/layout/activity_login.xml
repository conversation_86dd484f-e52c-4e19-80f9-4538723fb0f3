<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.login.LoginActivity">

    <ImageView
        android:id="@+id/bg_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/login_bg"
        android:visibility="gone"/>

    <!-- GIF背景 -->
    <androidx.media3.ui.PlayerView
        android:id="@+id/gifBackground"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clickable="false"
        android:elevation="0dp"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:resize_mode="zoom"
        app:use_controller="false" />

    <!-- 半透明遮罩 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#20000000"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 主要内容容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <!-- Callme标题 -->
        <ImageView
            android:id="@+id/callmeTitle"
            android:layout_width="202dp"
            android:layout_height="62dp"
            android:layout_marginBottom="25dp"
            android:src="@drawable/linkduo" />


        <!-- 登录按钮 -->
        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/loginButton"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginBottom="10dp"
            android:gravity="center"
            android:text="@string/start"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 社交登录按钮容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone">

            <com.score.callmetest.ui.widget.AlphaTextView
                android:id="@+id/btnMore"
                android:text="@string/str_more"
                android:textColor="@android:color/white"
                android:paddingHorizontal="10dp"
                android:paddingVertical="5dp"
                android:textSize="15sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <!-- Google登录按钮 -->
            <com.score.callmetest.ui.widget.CircleIconButton
                android:id="@+id/googleLoginButton"
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:contentDescription="@string/google_login"
                android:scaleType="center"
                android:visibility="gone"
                app:bgColor="@android:color/white"
                app:iconSrc="@drawable/ic_google" />

            <!-- 邮箱登录按钮 -->
            <com.score.callmetest.ui.widget.CircleIconButton
                android:id="@+id/emailLoginButton"
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:layout_marginStart="35dp"
                android:contentDescription="@string/email_login"
                android:scaleType="center"
                android:visibility="gone"
                app:bgColor="@android:color/white"
                app:iconSrc="@drawable/ic_email"
                app:tint="@android:color/black" />

        </LinearLayout>


        <!-- 条款同意区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:alpha="0.7"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/termsCheckBox"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:padding="8dp"
                android:button="@drawable/login_checkbox_selector"
                android:checked="true" />

            <TextView
                android:id="@+id/termsText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:includeFontPadding="false"
                android:lineSpacingExtra="2dp"
                android:text="By using our App you agree with our Terms &amp; Conditions and Privacy Policy"
                android:textColor="@android:color/white"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout> 