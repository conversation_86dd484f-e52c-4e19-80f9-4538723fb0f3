<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFDF5 ">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/bg_coin_1"
        android:elevation="1dp"
        android:layout_marginTop="18dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/line_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/bg_my_coin"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <Space
            android:id="@+id/space_top"
            android:layout_width="match_parent"
            android:layout_height="44dp" />

        <!-- 顶部栏：标题、金币数量 -->
        <LinearLayout
            android:id="@+id/llTopBar"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="13dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/str_store"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:fontFamily="@font/roboto_bold"
                android:textStyle="bold"
                android:gravity="start" />
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="11dp"
                android:paddingVertical="4dp"
                android:background="@drawable/bg_while_rounded">
                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:src="@drawable/coin" />

                <TextView
                    android:id="@+id/tvCoinBalance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="3dp"
                    android:text="100"
                    android:textSize="14sp"
                    android:textColor="@color/black"
                    android:fontFamily="@font/roboto_medium"/>

            </LinearLayout>

        </LinearLayout>

        <!-- Banner 轮播图区域 -->
        <LinearLayout
            android:id="@+id/line_banner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="10dp"
            android:paddingHorizontal="13dp">

            <com.score.callmetest.ui.widget.ViewPager2FrameLayoutHost
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/bannerViewPager"
                    android:layout_width="match_parent"
                    android:overScrollMode="never"
                    android:layout_height="106dp"/>

                <!-- 轮播图的小圆点显示-->
                <LinearLayout
                    android:id="@+id/dot_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:elevation="2dp"
                    android:layout_gravity="bottom|center_horizontal"
                    android:visibility="gone"
                    android:layout_marginBottom="5dp"/>
            </com.score.callmetest.ui.widget.ViewPager2FrameLayoutHost>

        </LinearLayout>

    </LinearLayout>



    <!-- 充值套餐列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvRechargeOptions"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="6dp"
        android:layout_marginStart="13dp"
        android:layout_marginEnd="13dp"
        android:overScrollMode="never"
        app:layout_constraintTop_toBottomOf="@+id/line_top"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>