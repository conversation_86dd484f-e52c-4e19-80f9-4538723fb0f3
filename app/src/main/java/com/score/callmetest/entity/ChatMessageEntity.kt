package com.score.callmetest.entity

import android.net.Uri
import android.os.Parcelable
import com.score.callmetest.Constant
import com.score.callmetest.network.GiftInfo
import kotlinx.parcelize.Parcelize

/**
 * 消息类型枚举
 */
enum class MessageType {
    TEXT,       // 文本消息
    IMAGE,      // 图片消息
    VOICE,      // 语音消息
    LINK,       // 链接消息
    FILE,       // 文件消息
    VIDEO,      // 视频消息
    CARD,       // 充值卡片消息
    GIFT,       // 礼物消息
    ROBOT      // 系统消息(机器人客服消息)
}

/**
 * 消息状态枚举
 */
enum class MessageStatus {
    SENDING,    // 发送中
    SENT,       // 已发送
    RECEIVED,   // 已接收
    READ,       // 已读
    FAILED      // 发送失败
}

object ContentType {
    const val GIFT = "gift" // 礼物
    const val RECHARGE_LINK = "recharge_link" // 充值链接
    const val RECHARGE_CARD = "tpp" // 充值卡片
}

/**
 * 聊天消息实体类
 * @property messageId 消息唯一ID
 * @property rcMsgId 对应融云消息ID--一般只有对方发送的这个才有值
 * @property senderId 发送者ID
 * @property senderName 发送者名称
 * @property senderAvatar 发送者头像URL
 * @property receiverId 接收者ID
 * @property content 消息内容
 * @property contentType 消息内容
 * @property messageType 消息类型
 * @property status 消息状态
 * @property timestamp 时间戳
 * @property progress 进度--比如图片下载
 * @property isChange 是否更改了---用于DiffUtils--暂时只用于语音播放动画
 * @property isPlaying 是否正在播放--比如语音
 * @property isCurrentUser 是否是当前用户发送的消息
 * @property thumbUri -缩略图
 * @property mediaUri 远程媒体URL(图片、语音、视频等)
 * @property mediaLocalUri 本地媒体URL(图片、语音、视频等)
 * @property mediaDuration 媒体时长(语音、视频)
 * @property giftInfo 礼物信息
 * @property isAutoTrans 是否自动翻译
 * @property extra extra信息
 */
@Parcelize
data class ChatMessageEntity(
    val messageId: String,
    var rcMsgId: String? = null,
    val currentUserId: String,
    val senderId: String,
    val senderName: String,
    val senderAvatar: String,
    val receiverId: String,
    val content: String,
    val contentType: String? = null,
    val messageType: MessageType = MessageType.TEXT,
    var status: MessageStatus = MessageStatus.SENDING,
    var timestamp: Long = System.currentTimeMillis(),
    var progress: Int = 0,
    var isChange: Boolean = false,
    var isPlaying: Boolean = false,
    val isCurrentUser: Boolean,
    var thumbUri: Uri? = null,
    var mediaUri: Uri? = null,
    var mediaLocalUri: Uri? = null,
    val mediaDuration: Long = 0,
    val giftInfo: GiftInfo? = null,
    var isAutoTrans: Boolean = true,
    var extra: String? = null
) : Parcelable {
    
    /**
     * 创建一个新的消息对象，更新消息状态
     * @param newStatus 新的消息状态
     * @return 新的消息对象
     */
    fun withStatus(newStatus: MessageStatus): ChatMessageEntity {
        return copy(status = newStatus)
    }
}

/**
 * 是否是机器人客服
 * @return [Boolean]
 */
fun ChatMessageEntity.isRobot(): Boolean{
    return this.messageType == MessageType.ROBOT ||
            this.senderId == Constant.ROBOt_ID
}