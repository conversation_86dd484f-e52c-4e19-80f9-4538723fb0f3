package com.score.callmetest.ui.widget

import androidx.fragment.app.FragmentManager
import com.score.callmetest.manager.CountdownManager

/**
 * 促销弹窗辅助类，方便在任何地方调用显示促销弹窗
 * 目前有两个xml：
 * 1、dialog_promotion.xml - 新用户促销弹窗，包含svga_discount动画
 * 2、dialog_promotion1.xml - 活动促销弹窗，包含tv_day和tv_add_amount
 *
 * 动画文件：
 * - 按钮动画：btn_promotion.svga
 * - 折扣标签动画：discount_tags.svga（仅dialog_promotion.xml使用）
 *
 * 倒计时使用：tv_hour（时）、tv_min（分）、tv_seconds（秒）
 * 价格显示：tv_price（现价）、tv_old_price（原价）
 */
class PromotionDialogHelper {
    companion object {
        /**
         * 显示促销弹窗
         *
         * @param fragmentManager FragmentManager
         * @param amount 金额文本
         * @param addAmount 额外金额文本（带+号）
         * @param description 描述文本
         * @param price 当前价格
         * @param layoutResId 布局资源ID
         * @param buttonSvgaName 按钮SVGA动画名称
         * @param buttonEffectSvgaName 按钮特效SVGA动画名称
         * @param remainingCount 剩余数量
         * @param treasureBoxImageUrl 宝箱图片URL，如果为空则使用默认图片
         * @param onButtonClickListener 按钮点击回调
         * @param activityType 活动类型，用于从CountdownManager获取倒计时
         * @param activityId 活动ID，用于从CountdownManager获取倒计时
         */
        fun showPromotionDialog(
            fragmentManager: FragmentManager,
            amount: String,
            description: String,
            price: String = "",
            originPrice: String = "",
            layoutResId: Int = com.score.callmetest.R.layout.dialog_promotion1,
            buttonSvgaName: String = "btn_promotion.svga",
            buttonEffectSvgaName: String = "discount_tags.svga",
            onButtonClickListener: (() -> Unit)? = null,
            addAmount: String = "",
            remainingCount: Int = 0,
            treasureBoxImageUrl: String? = null,
            activityType: CountdownManager.ActivityType,
            activityId: String
        ) {
            val dialog = PromotionDialogFragment.newInstance(
                layoutResId = layoutResId,
                amount = amount,
                description = description,
                price = price,
                originPrice = originPrice,
                buttonSvgaName = buttonSvgaName,
                buttonEffectSvgaName = buttonEffectSvgaName,
                onButtonClickListener = onButtonClickListener,
                addAmount = addAmount,
                remainingCount = remainingCount,
                treasureBoxImageUrl = treasureBoxImageUrl,
                activityType = activityType,
                activityId = activityId
            )
            dialog.show(fragmentManager, "PromotionDialog")
        }
    }
}