package com.score.callmetest.ui.chat.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.databinding.ItemBroadcasterCardBinding
import com.score.callmetest.databinding.ItemChatMessageGiftBinding
import com.score.callmetest.databinding.ItemChatMessageLeftBinding
import com.score.callmetest.databinding.ItemChatMessageRechargeCardBinding
import com.score.callmetest.databinding.ItemChatMessageRightBinding
import com.score.callmetest.databinding.ItemChatMessageVoiceLeftBinding
import com.score.callmetest.databinding.ItemChatMessageVoiceRightBinding
import com.score.callmetest.entity.ChatListItem
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageType
import kotlinx.coroutines.CoroutineScope

/**
 * 聊天消息适配器
 * 支持多种消息类型和左右布局，以及主播card
 */
class ChatMessageAdapter(val scope: CoroutineScope) : ListAdapter<ChatListItem, RecyclerView.ViewHolder>(ChatListItemDiffCallback()) {

    /**
     *  消息涉及到的所有listener
     */
    private val mChatMessageListeners = ChatAdapterListeners()

    companion object {
        private const val VIEW_TYPE_BROADCASTER_CARD = 0  // 主播card
        private const val VIEW_TYPE_SENT_TEXT = 1     // 发送的文本消息
        private const val VIEW_TYPE_RECEIVED_TEXT = 2 // 接收的文本消息
        private const val VIEW_TYPE_SENT_IMAGE = 3    // 发送的图片消息
        private const val VIEW_TYPE_RECEIVED_IMAGE = 4 // 接收的图片消息
        private const val VIEW_TYPE_SENT_VOICE = 5    // 发送的语音消息
        private const val VIEW_TYPE_RECEIVED_VOICE = 6 // 接收的语音消息
        private const val VIEW_TYPE_GIFT = 7          // 礼物消息
        private const val VIEW_TYPE_RECHARGE_CARD = 8 // 充值卡片消息
        private const val VIEW_TYPE_EMPTY_STATE = 9   // 空状态

        private const val PAY_LOAD_STATUS = 0x101          // 发送状态更新
        private const val PAY_LOAD_AUDIO_PLAY = 0x102          // 语音播放动画更新
    }

    // <editor-folder desc="对外事件">

    /**
     * 设置消息点击监听(text、voice、card)
     */
    fun setOnMessageClickListener(listener: (ChatMessageEntity, View) -> Unit) {
        mChatMessageListeners.mOnMessageClickListener= listener
    }

    /**
     * 设置消息长按监听(text、voice)
     */
    fun setOnMessageLongClickListener(listener: (ChatMessageEntity, View) -> Boolean) {
        mChatMessageListeners.mOnMessageLongClickListener = listener
    }

    /**
     * 设置重发按钮点击监听
     */
    fun setOnResendClickListener(listener: (ChatMessageEntity) -> Unit) {
        mChatMessageListeners.mOnResendClickListener = listener
    }

    /**
     * 设置图片点击监听
     */
    fun setOnImageClickListener(listener: (ChatMessageEntity, View) -> Unit) {
        mChatMessageListeners.mOnImageClickListener = listener
    }

    /**
     * 设置图长按片点击监听
     */
    fun setOnImageLongClickListener(listener: (ChatMessageEntity, View) -> Boolean) {
        mChatMessageListeners.mOnImageLongClickListener = listener
    }

    /**
     * 设置头像点击监听
     */
    fun setOnAvatarClickListener(listener: (ChatMessageEntity) -> Unit) {
        mChatMessageListeners.mOnAvatarClickListener = listener
    }

    /**
     * 设置翻译点击监听
     */
    fun setOnTranslateClickListener(listener: (ChatMessageEntity) -> Unit) {
        mChatMessageListeners.mOnTranslateClickListener = listener
    }

    /**
     * 设置FAQ问题点击监听器
     * @param listener FAQ问题点击监听器，参数为(faqCode: Int, message: ChatMessageEntity) -> Unit
     */
    fun setOnFaqQuestionClickListener(listener: (Int, ChatMessageEntity) -> Unit) {
        mChatMessageListeners.mOnFaqQuestionClickListener = listener
    }

    /**
     * 设置主播card相册图片点击监听器
     * @param listener 相册图片点击监听器，参数为(photoUrl: String, position: Int, allUrls: List<String>) -> Unit
     */
    fun setOnPhotoClickListener(listener: (String, Int, List<String>) -> Unit) {
        mChatMessageListeners.mOnPhotoClickListener = listener
    }


    /**
     * 充值卡片查看详情点击监听
     * @param listener
     */
    fun setOnRechargeCardDetailsClickListener(listener: (ChatMessageEntity) -> Unit) {
        mChatMessageListeners.mOnRechargeCardDetailsClickListener = listener
    }

    // </editor-folder>

    override fun getItemViewType(position: Int): Int {
        val item = getItem(position)

        return when (item) {
            is ChatListItem.BroadcasterCard -> VIEW_TYPE_BROADCASTER_CARD
            is ChatListItem.EmptyState -> VIEW_TYPE_EMPTY_STATE
            is ChatListItem.Message -> {
                val message = item.messageEntity
                when (message.messageType) {
                    MessageType.TEXT -> {
                        if (message.isCurrentUser) VIEW_TYPE_SENT_TEXT else VIEW_TYPE_RECEIVED_TEXT
                    }
                    MessageType.IMAGE -> {
                        if (message.isCurrentUser) VIEW_TYPE_SENT_IMAGE else VIEW_TYPE_RECEIVED_IMAGE
                    }
                    MessageType.VOICE -> {
                        if (message.isCurrentUser) VIEW_TYPE_SENT_VOICE else VIEW_TYPE_RECEIVED_VOICE
                    }
                    MessageType.GIFT -> {
                        VIEW_TYPE_GIFT
                    }
                    MessageType.CARD -> {
                        VIEW_TYPE_RECHARGE_CARD
                    }
                    MessageType.LINK -> {
                        // 这个都是主播发送给用户的--算是文本
                        VIEW_TYPE_RECEIVED_TEXT
                    }
                    MessageType.ROBOT -> {
                        // FAQ消息都是客服发送给用户的
                        VIEW_TYPE_RECEIVED_TEXT
                    }
                    else -> {
                        if (message.isCurrentUser) VIEW_TYPE_SENT_TEXT else VIEW_TYPE_RECEIVED_TEXT
                    }
                }
            }
        }
    }



    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_BROADCASTER_CARD -> {
                // 主播card
                val binding = ItemBroadcasterCardBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                BroadcasterCardViewHolder(binding, mChatMessageListeners)
            }
            VIEW_TYPE_EMPTY_STATE -> {
                // 空状态
                EmptyStateViewHolder(parent)
            }
            VIEW_TYPE_SENT_TEXT, VIEW_TYPE_SENT_IMAGE -> {
                // 文本、图片发送
                val binding = ItemChatMessageRightBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                SentMessageViewHolder(binding, mChatMessageListeners)
            }
            VIEW_TYPE_RECEIVED_TEXT, VIEW_TYPE_RECEIVED_IMAGE -> {
                // 文本、图片接收
                val binding = ItemChatMessageLeftBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ReceivedMessageViewHolder(scope, binding, mChatMessageListeners)
            }
            VIEW_TYPE_SENT_VOICE -> {
                // 语音发送
                val binding = ItemChatMessageVoiceRightBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                SentVoiceMessageViewHolder(binding, mChatMessageListeners)
            }
            VIEW_TYPE_RECEIVED_VOICE -> {
                // 语音接收
                val binding = ItemChatMessageVoiceLeftBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ReceivedVoiceMessageViewHolder(binding, mChatMessageListeners)
            }
            VIEW_TYPE_GIFT -> {
                // 礼物发送
                val binding = ItemChatMessageGiftBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                GiftMessageViewHolder(binding, mChatMessageListeners)
            }
            VIEW_TYPE_RECHARGE_CARD -> {
                // 充值卡片消息
                val binding = ItemChatMessageRechargeCardBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                RechargeCardMessageViewHolder(binding, mChatMessageListeners)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int, payloads: List<Any?>) {
        val item = getItem(position)

        // 主播card不需要处理payloads
        if (holder is BroadcasterCardViewHolder) {
            super.onBindViewHolder(holder, position, payloads)
            return
        }

        if(payloads.isEmpty()){
            super.onBindViewHolder(holder, position, payloads)
            return
        }

        // 只有消息类型才处理payloads
        if (item !is ChatListItem.Message) {
            super.onBindViewHolder(holder, position, payloads)
            return
        }

        // 判断局部修改位置
        val payLoad = payloads[0]
        if(payLoad == PAY_LOAD_STATUS){
            (holder as MessageHolder).updateStatus(item.messageEntity.status)
            return
        }

        when (holder) {
            is SentMessageViewHolder -> {
            }
            is ReceivedMessageViewHolder -> {
            }
            is SentVoiceMessageViewHolder, is ReceivedVoiceMessageViewHolder -> {
                // 语音消息
                val msg = item.messageEntity.apply {
                    if(isPlaying) {
                        holder.startPlayAnimation()
                    }else {
                        holder.stopPlayAnimation()
                    }
                    isChange = false
                }
                holder.updateEntity(msg)
            }
            is GiftMessageViewHolder -> {

            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = getItem(position)

        when (holder) {
            is BroadcasterCardViewHolder -> {
                // 绑定主播card数据
                if (item is ChatListItem.BroadcasterCard) {
                    holder.bind(item.userInfo)
                }
            }
            is EmptyStateViewHolder -> {
                // 空状态不需要绑定数据
            }
            is SentMessageViewHolder -> {
                if (item is ChatListItem.Message) {
                    holder.bind(item.messageEntity)
                }
            }
            is ReceivedMessageViewHolder -> {
                if (item is ChatListItem.Message) {
                    holder.bind(item.messageEntity)
                }
            }
            is SentVoiceMessageViewHolder, is ReceivedVoiceMessageViewHolder -> {
                // 语音消息
                if (item is ChatListItem.Message) {
                    val message = item.messageEntity
                    holder.bind(message)

                    // 如果这个消息正在播放，显示播放动画
                    if (message.isPlaying) {
                        holder.startPlayAnimation()
                    } else {
                        holder.stopPlayAnimation()
                    }
                }
            }
            is GiftMessageViewHolder -> {
                if (item is ChatListItem.Message) {
                    holder.bind(item.messageEntity)
                }
            }
            is RechargeCardMessageViewHolder -> {
                if (item is ChatListItem.Message) {
                    holder.bind(item.messageEntity)
                }
            }
        }
    }

    /**
     * 聊天列表项差异比较回调
     */
    private class ChatListItemDiffCallback : DiffUtil.ItemCallback<ChatListItem>() {
        override fun areItemsTheSame(oldItem: ChatListItem, newItem: ChatListItem): Boolean {
            return oldItem.getItemId() == newItem.getItemId()
        }

        override fun areContentsTheSame(oldItem: ChatListItem, newItem: ChatListItem): Boolean {
            return when {
                oldItem is ChatListItem.BroadcasterCard && newItem is ChatListItem.BroadcasterCard -> {
                    // 主播card内容比较 - 只比较用户信息
                    oldItem.userInfo == newItem.userInfo
                }
                oldItem is ChatListItem.Message && newItem is ChatListItem.Message -> {
                    // 消息内容比较
                    oldItem.messageEntity == newItem.messageEntity
                }
                oldItem is ChatListItem.EmptyState && newItem is ChatListItem.EmptyState -> {
                    // 空状态始终相同
                    true
                }
                else -> false
            }
        }

        override fun getChangePayload(oldItem: ChatListItem, newItem: ChatListItem): Any? {
            // 主播card和空状态不需要payload更新
            if (oldItem is ChatListItem.BroadcasterCard || newItem is ChatListItem.BroadcasterCard ||
                oldItem is ChatListItem.EmptyState || newItem is ChatListItem.EmptyState) {
                return null
            }

            // 只处理消息类型的payload
            if (oldItem is ChatListItem.Message && newItem is ChatListItem.Message) {
                val oldMessage = oldItem.messageEntity
                val newMessage = newItem.messageEntity

                if(oldMessage.status != newMessage.status){
                    // 更新状态
                    return PAY_LOAD_STATUS
                }
                when (newMessage.messageType) {
                    MessageType.VOICE -> {
                        // 语音 -- 播放是否
                        return if(newMessage.isChange) {
                            PAY_LOAD_AUDIO_PLAY
                        }else {
                            // 发送状态或其他更改
                            null
                        }
                    }
                    MessageType.IMAGE -> {
                        // 图片
                    }
                    else -> return null
                }
            }
            return null
        }
    }
}