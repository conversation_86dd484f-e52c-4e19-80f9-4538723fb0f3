package com.score.callmetest.ui.home

import android.animation.AnimatorSet
import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.style.ImageSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.score.callmetest.BuildConfig
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentHomeBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.FlashChatManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.main.MainActivity
import com.score.callmetest.ui.match.MatchActivity
import com.score.callmetest.ui.widget.CoinRechargeDialog
import com.score.callmetest.ui.widget.Helper.PagerHelper
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.AnimatorUtil
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.click
import timber.log.Timber


class HomeFragment : BaseFragment<FragmentHomeBinding, HomeViewModel>() {

    private var currentTab1Position = 0

    // 记录每个一级Tab下的二级Tab索引
    private var tab2SelectedPositions = mutableMapOf<Int, Int>()
    private lateinit var tab1Layout: TabLayout
    private lateinit var tab2Layout: TabLayout
    private lateinit var viewPager: ViewPager2
    private var countryFilterPopup: CountryFilterPopupWindow? = null

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentHomeBinding {
        return FragmentHomeBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = HomeViewModel::class.java

    override fun initView() {
        tab1Layout = binding.tabLayout1
        tab2Layout = binding.tabLayout2
        viewPager = binding.viewPager

        // 设置TabLayout的稳定性配置
        tab1Layout.tabRippleColor = null
        tab2Layout.tabRippleColor = null

        setupTabs()
        setupViewPager()
        setupCountryFilter()
        setupFlash()
    }

    override fun initListener() {
        // 监听主播墙滑动状态
        if (!StrategyManager.isReviewPkg()) {
            EventBus.observe(this, HomeViewModel.WallScrollEvent::class.java) { event ->
                when (event.state) {
                    RecyclerView.SCROLL_STATE_IDLE -> {
                        // 滑动停止时显示MainActivity的fab1和fab2
                        (activity as? MainActivity)?.setFab1Visible(true)
                        (activity as? MainActivity)?.setFabVisible(true)
                        refreshFlash()
                    }
                    else -> {
                        binding.flashChatLayout.visibility = View.GONE
                        // 滑动时隐藏MainActivity的fab1和fab2
                        (activity as? MainActivity)?.setFab1Visible(false)
                        (activity as? MainActivity)?.setFabVisible(false)
                    }
                }
            }
        }

        // fixme: 测试时不出现机器人来电
        if (!BuildConfig.DEBUG) {
            UserInfoManager.getPresentedCoin(
                scope = lifecycleScope,
            ) {

            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
    }

    private fun setupFlash() {
        if (FlashChatManager.canShowFlashChat()) {
            EventBus.observe(this, WallViewModel.FlashBroadcasterIconsEvent::class.java) { event ->
                if (event.broadcasterList.isNotEmpty()) {
                    flashIconAnim(event.broadcasterList)
                }
            }

            DualChannelEventManager.observeFlashChatFreeTimes(this) { event ->
                FlashChatManager.matchFreeTimes = event.freeTimes
                refreshFlash()
            }

            // 先展示默认图
            GlideUtils.load(
                view = binding.flashIcon2,
                url = "",
                placeholder = R.drawable.placeholder
            )
            FlashChatManager.init()
            refreshFlash()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun refreshFlash() {
        if (!FlashChatManager.canShowFlashChat()) {
            binding.flashChatLayout.visibility = View.GONE
            return
        }
        binding.flashChatLayout.visibility = View.VISIBLE
        binding.flashChatLayout.doOnPreDraw { it ->
            it.background = DrawableUtils.createGradientDrawable(
                colors = intArrayOf(Color.WHITE, Color.TRANSPARENT),
                orientation = GradientDrawable.Orientation.TOP_BOTTOM,
                radius = it.height / 2f
            )
        }

        if (StrategyManager.strategyConfig?.flashChatConfig?.isFreeCall == true
            && FlashChatManager.matchFreeTimes > 0
        ) {
            binding.flashContentLayout.doOnPreDraw { it ->
                it.background = DrawableUtils.createGradientDrawable(
                    colors = intArrayOf("#56F865".toColorInt(), "#00B8D3".toColorInt()),
                    orientation = GradientDrawable.Orientation.TOP_BOTTOM,
                    radius = it.height / 2f
                )
            }
            binding.flashText.text = "Free Match"
            binding.flashTip.text = "${FlashChatManager.matchFreeTimes} time left"

            binding.flashChatLayout.click {
                ActivityUtils.startActivity(requireActivity(), MatchActivity::class.java)
            }
        } else {
            binding.flashContentLayout.doOnPreDraw { it ->
                it.background = DrawableUtils.createGradientDrawable(
                    colors = intArrayOf("#FFDC20".toColorInt(), "#FF462F".toColorInt()),
                    orientation = GradientDrawable.Orientation.TOP_BOTTOM,
                    radius = it.height / 2f
                )
            }

            val price = FlashChatManager.getMatchCoins()
            binding.flashTip.text = CustomUtils.createCoinSpannableText(
                context = requireContext(),
                text = "(icon ${price}/min)",
                coinSizeDp = 14f,
                alignment = ImageSpan.ALIGN_BOTTOM,
            )

            price?.let { p ->
                UserInfoManager.myUserInfo?.availableCoins?.let {
                    if (it > p) {
                        binding.flashChatLayout.click {
                            ActivityUtils.startActivity(requireActivity(), MatchActivity::class.java)
                        }
                    } else {
                        binding.flashChatLayout.click {
                            CoinRechargeDialog(RechargeSource.FLASH).show(requireActivity().supportFragmentManager, "coin_recharge")
                        }
                    }
                }
            }
        }
    }

    private var animationSet1: AnimatorSet? = null
    private var animationSet2: AnimatorSet? = null
    private var animAvatarList = mutableListOf<BroadcasterModel>()
    private var animAvatarIndex = 0
    private fun doAnim1() {
        try {
            if (animAvatarList.isNotEmpty() &&
                animAvatarIndex < animAvatarList.size
            ) {
                if (isDetached) {
                    return
                }
                GlideUtils.load(
                    view = binding.flashIcon1,
                    url = animAvatarList[animAvatarIndex++].avatar,
                    placeholder = R.drawable.placeholder
                )
                animAvatarIndex = animAvatarIndex % animAvatarList.size
            }

            binding.flashIconLayout.removeView(binding.flashIcon1)
            binding.flashIconLayout.addView(binding.flashIcon1)
            animationSet1 = AnimatorUtil.translate(
                view = binding.flashIcon1,
                fromX = DisplayUtils.dp2pxInternalFloat(15f),
                toX = 0f,
                translateDuration = 500,
                onEnd = {
                    ThreadUtils.runOnMainDelayed(800) {
                        if (isDetached) {
                            return@runOnMainDelayed
                        }
                        doAnim2()
                        AnimatorUtil.fadeOut(
                            view = binding.flashIcon1,
                            fadeDuration = 500,
                            onEnd = {
//                                ThreadUtils.runOnMainDelayed(800) {
//                                    doAnim1()
//                                }
                            }
                        )
                    }
                }
            )
        } catch (e: Exception) {

        }
    }

    private fun doAnim2() {
        try {
            if (animAvatarList.isNotEmpty() &&
                animAvatarIndex < animAvatarList.size
            ) {
                if (isDetached) {
                    return
                }
                GlideUtils.load(
                    view = binding.flashIcon2,
                    url = animAvatarList[animAvatarIndex++].avatar,
                    placeholder = R.drawable.placeholder
                )
                animAvatarIndex = animAvatarIndex % animAvatarList.size
            }
            binding.flashIconLayout.removeView(binding.flashIcon2)
            binding.flashIconLayout.addView(binding.flashIcon2)
            animationSet2 = AnimatorUtil.translate(
                view = binding.flashIcon2,
                fromX = DisplayUtils.dp2pxInternalFloat(15f),
                toX = 0f,
                translateDuration = 500,
                onEnd = {
                    ThreadUtils.runOnMainDelayed(800) {
                        if (isDetached) {
                            return@runOnMainDelayed
                        }
                        doAnim1()
                        AnimatorUtil.fadeOut(
                            view = binding.flashIcon2,
                            fadeDuration = 500,
                            onEnd = {
//                                ThreadUtils.runOnMainDelayed(800) {
//                                    doAnim2()
//                                }
                            }
                        )
                    }
                }
            )
        } catch (e: Exception) {

        }
    }

    private fun startAnims() {
        doAnim1()
    }

    private fun flashIconAnim(broadcasterList: List<BroadcasterModel>) {
        if (broadcasterList.isNotEmpty() && animationSet1 == null) {
            animAvatarList.clear()
            animAvatarList.addAll(broadcasterList.subList(0, 6.coerceAtMost(broadcasterList.size)))
            if (broadcasterList.size > 1) {
                GlideUtils.load(
                    view = binding.flashIcon2,
                    url = broadcasterList[1].avatar,
                    placeholder = R.drawable.placeholder
                )
            }
            startAnims()
            // 创建动画集，组合两个动画
            // 启动动画
//            binding.flashIcon1.startAnimation(animationSet1)
//            binding.flashIcon2.startAnimation(animationSet2)
        }
    }

    private fun setupTabs() {
        // 设置一级Tab的选择监听
        tab1Layout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                updateTabAppearance(tab, true, false) // tab1Layout
                currentTab1Position = tab.position
                updateTab2(currentTab1Position)
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                updateTabAppearance(tab, false, false) // tab1Layout
            }

            override fun onTabReselected(tab: TabLayout.Tab) {}
        })

        // 设置二级Tab的选择监听
        tab2Layout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                updateTabAppearance(tab, true, true) // tab2Layout

                if (!updatingTab2) {
                    viewPager.currentItem = getGlobalPosition(currentTab1Position, tab.position)
                }

                // 当tab1为第一个标签时，更新国家筛选UI
                if (currentTab1Position == 0) {
                    val tab2Name =
                        viewModel.broadcasterWallTabDataLists?.get(currentTab1Position)?.subTagList?.get(
                            tab.position
                        )
                    // 查找与tab2文本匹配的国家代码
                    val countryCode =
                        tab2Name?.let { CountryUtils.getCountryByEnName(it)?.iso ?: "ALL" }
                    updateCountryFilterUI(countryCode)
                    // 更新ViewModel中的国家代码
                    // viewModel.setSelectedCountry(countryCode)
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                updateTabAppearance(tab, false, true) // tab2Layout
            }

            override fun onTabReselected(tab: TabLayout.Tab) {}
        })

        // 初始化一级Tab
        clearTabLayout(tab1Layout)

        viewModel.broadcasterWallTabDataLists?.forEachIndexed { index, tab1Data ->
            val tab = tab1Layout.newTab()
            tab.customView = createCustomTabView(tab1Data.tagName ?: "", index == 0, false) // tab1Layout
            tab1Layout.addTab(tab)
        }
        // 初始化tab2记录表
        for (i in 0..tab1Layout.tabCount - 1) {
            tab2SelectedPositions.put(i, 0)
        }
        // 初始化二级Tab
        clearTabLayout(tab2Layout)
        updateTab2(0)
    }

    private fun createCustomTabView(text: String, selected: Boolean, isTab2: Boolean = false): View {
        val view = layoutInflater.inflate(R.layout.tab_custom, null)
        val textView = view.findViewById<TextView>(R.id.tab_text)
        val indicator = view.findViewById<View>(R.id.tab_indicator)
        textView.text = text

        // 区分tab1和tab2的字体大小
        if (isTab2) {
            // tab2Layout: 字体大小14f，选中时加粗
            textView.textSize = if (selected) 16f else 14f
            textView.paint.isFakeBoldText = false
            textView.setTypeface(
                if (selected) Typeface.create("sans-serif", Typeface.BOLD)
                else Typeface.create("sans-serif", Typeface.NORMAL)
            )
        } else {
            // tab1Layout: 原有样式，选中22f，未选中15f
            textView.textSize = if (selected) 22f else 15f
            textView.paint.isFakeBoldText = false
            textView.setTypeface(
                if (selected) Typeface.create("sans-serif", Typeface.BOLD)
                else Typeface.create("sans-serif", Typeface.NORMAL)
            )
        }

        // 先重置indicator
        indicator.visibility = if (selected) View.VISIBLE else View.GONE

        // 使用ViewTreeObserver避免post操作的不稳定性
        if (selected) {
            textView.viewTreeObserver.addOnGlobalLayoutListener(object :
                ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    textView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    if (textView.width > 0) {
                        indicator.layoutParams.width = textView.width / 2
                        indicator.layoutParams.height = textView.height / 22
                        indicator.background = DrawableUtils.createRoundRectDrawable(
                            resources.getColor(R.color.tab_indicator_color),
                            textView.height / 4f
                        )
                        indicator.requestLayout()
                    }
                }
            })
        }
        return view
    }

    private fun setupViewPager() {
        viewPager.adapter = object : FragmentStateAdapter(this) {
            override fun getItemCount(): Int = getTotalItemCount()

            private fun newWallFragment(position: Int): Fragment {
                val (tab1Index, tab2Index) = getTab1AndTab2Index(position)
                val tab1Name = viewModel.broadcasterWallTabDataLists!![tab1Index].tagName ?: ""
                val tab2Name =
                    viewModel.broadcasterWallTabDataLists!![tab1Index].subTagList?.get(tab2Index)
                        ?: "All"

                // region参数通过arguments传递
                return WallFragment.newInstance(
                    tab1Name,
                    tab2Name,
                    viewModel.getSelectedCountryCode()
                )
            }

            override fun createFragment(position: Int): Fragment {
                return newWallFragment(position)
            }
        }

        GlobalManager.setNeverOverScroll(binding.viewPager)

        viewPager.offscreenPageLimit = 3
        // 设置用户输入类型，减少不必要的滑动冲突
        viewPager.isUserInputEnabled = true

        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                val (tab1Index, tab2Index) = getTab1AndTab2Index(position)
                // 只在这里更新tab2最新记录
                tab2SelectedPositions[tab1Index] = tab2Index

                // 避免在更新Tab2期间触发额外的同步
                if (!updatingTab2) {
                    if (currentTab1Position != tab1Index) {
                        // 使用post确保Tab切换的顺序正确
                        tab1Layout.post {
                            tab1Layout.getTabAt(tab1Index)?.select()
                        }
                    }

                    // 确保二级Tab存在且可见时才选择
                    if (tab2Layout.isVisible && tab2Layout.tabCount > tab2Index) {
                        tab2Layout.post {
                            tab2Layout.getTabAt(tab2Index)?.select()
                        }
                    }
                }
                checkCountryFilter()
            }
        })
    }

    // 添加辅助方法来更新Tab外观
    private fun updateTabAppearance(tab: TabLayout.Tab?, selected: Boolean, isTab2: Boolean = false) {
        val view = tab?.customView
        val textView = view?.findViewById<TextView>(R.id.tab_text)
        val indicator = view?.findViewById<View>(R.id.tab_indicator)

        if (selected) {
            if (isTab2) {
                // tab2Layout: 字体大小14f，选中时加粗
                textView?.textSize = 16f
            } else {
                // tab1Layout: 选中时22f
                textView?.textSize = 22f
            }
            textView?.paint?.isFakeBoldText = false
            textView?.setTypeface(Typeface.create("sans-serif", Typeface.BOLD))
            indicator?.visibility = View.VISIBLE

            // 使用ViewTreeObserver确保布局稳定
            textView?.viewTreeObserver?.addOnGlobalLayoutListener(object :
                ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    textView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    if (textView.width > 0 && indicator != null) {
                        indicator.layoutParams.width = textView.width / 2
                        indicator.layoutParams.height = textView.height / 2
                        indicator.background = DrawableUtils.createRoundRectDrawable(
                            resources.getColor(R.color.tab_indicator_color),
                            textView.height / 4f
                        )
                        indicator.requestLayout()
                    }
                }
            })
        } else {
            if (isTab2) {
                // tab2Layout: 字体大小14f，未选中时正常字体
                textView?.textSize = 14f
            } else {
                // tab1Layout: 未选中时15f
                textView?.textSize = 15f
            }
            textView?.paint?.isFakeBoldText = false
            textView?.setTypeface(Typeface.create("sans-serif", Typeface.NORMAL))
            indicator?.visibility = View.GONE
        }
    }

    var updatingTab2: Boolean = false
    private fun updateTab2(tab1Position: Int) {
        updatingTab2 = true
        val subTagList = viewModel.broadcasterWallTabDataLists?.get(tab1Position)?.subTagList
        // 判断是否只下发了一个“All”
        if (subTagList != null && subTagList.size == 1 && subTagList[0].equals(
                "All",
                ignoreCase = true
            )
        ) {
            // 使用动画隐藏二级Tab，避免突然消失
            if (tab2Layout.isVisible) {
                tab2Layout.animate()
                    .alpha(0f)
                    .setDuration(150)
                    .withEndAction {
                        tab2Layout.visibility = View.GONE
                        tab2Layout.alpha = 1f
                    }
                    .start()
            }
            viewPager.currentItem = getGlobalPosition(currentTab1Position, 0)
        } else {
            // 确保二级Tab可见
            if (tab2Layout.visibility != View.VISIBLE) {
                tab2Layout.alpha = 0f
                tab2Layout.visibility = View.VISIBLE
                tab2Layout.animate()
                    .alpha(1f)
                    .setDuration(150)
                    .start()
            }

            // 只有在Tab内容真正改变时才重建
            val needRebuild = tab2Layout.tabCount != (subTagList?.size ?: 0) ||
                    subTagList?.mapIndexed { index, tabData ->
                        val existingTab = tab2Layout.getTabAt(index)
                        val existingText =
                            existingTab?.customView?.findViewById<TextView>(R.id.tab_text)?.text
                        existingText != tabData
                    }?.any { it == true } ?: true

            if (needRebuild) {
                tab2Layout.removeAllTabs()
                subTagList?.forEach { tab2Data ->
                    val tab = tab2Layout.newTab()
                    tab.customView = createCustomTabView(tab2Data, false, true) // tab2Layout
                    tab2Layout.addTab(tab)
                }

                // 恢复选中状态
                val tab2Index = tab2SelectedPositions.getOrElse(tab1Position) { 0 }
                if (tab2Layout.tabCount > 0) {
                    val targetIndex = tab2Index.coerceAtMost(tab2Layout.tabCount - 1)
                    // 延迟选择，确保Tab已经完全添加
                    tab2Layout.post {
                        if (targetIndex == 0 && tab2Layout.selectedTabPosition != 0) {
                            // 手动触发第一个Tab的选中状态
                            tab2Layout.getTabAt(0)?.select()
                            updateTabAppearance(tab2Layout.getTabAt(0), true)
                        } else if (targetIndex > 0) {
                            tab2Layout.getTabAt(targetIndex)?.select()
                        }
                        viewPager.currentItem = getGlobalPosition(currentTab1Position, targetIndex)
                    }
                }
            } else {
                // 如果不需要重建，只更新选中状态
                val tab2Index = tab2SelectedPositions.getOrElse(tab1Position) { 0 }
                if (tab2Layout.tabCount > 0) {
                    val targetIndex = tab2Index.coerceAtMost(tab2Layout.tabCount - 1)
                    if (tab2Layout.selectedTabPosition != targetIndex) {
                        tab2Layout.getTabAt(targetIndex)?.select()
                    }
                    viewPager.currentItem = getGlobalPosition(currentTab1Position, targetIndex)
                }
            }
        }
        updatingTab2 = false
    }

    /**
     * 把所有的subtab相加
     */
    private fun getTotalItemCount(): Int {
        if (viewModel.broadcasterWallTabDataLists == null) {
            return 0
        }
        return viewModel.broadcasterWallTabDataLists!!.sumOf { it.subTagList?.size ?: 0 }
    }

    /**
     * 获取当前的Fragment属于总数的第几个
     */
    private fun getGlobalPosition(tab1Position: Int, tab2Position: Int): Int {
        if (viewModel.broadcasterWallTabDataLists == null) {
            return 0
        }
        var position = 0
        for (i in 0 until tab1Position) {
            position += viewModel.broadcasterWallTabDataLists!![i].subTagList?.size ?: 0
        }
        return position + tab2Position
    }

    private fun getTab1AndTab2Index(globalPosition: Int): Pair<Int, Int> {
        if (viewModel.broadcasterWallTabDataLists == null) {
            return Pair(0, 0)
        }
        var remainingPosition = globalPosition
        for (i in viewModel.broadcasterWallTabDataLists!!.indices) {
            val subTabsSize = viewModel.broadcasterWallTabDataLists!![i].subTagList?.size ?: 0
            if (remainingPosition < subTabsSize) {
                return Pair(i, remainingPosition)
            }
            remainingPosition -= subTabsSize
        }
        return Pair(0, 0)
    }

    // 彻底清理 TabLayout 的所有子 View，防止重影
    private fun clearTabLayout(tabLayout: TabLayout) {
        tabLayout.removeAllTabs()
        for (i in 0 until tabLayout.childCount) {
            val child = tabLayout.getChildAt(i)
            if (child is ViewGroup) {
                child.removeAllViews()
            }
        }
    }

    private fun checkCountryFilter() {
        binding.countryFilter.visibility =
            if (currentTab1Position == 0
                && !StrategyManager.isReviewPkg()
                && tab2SelectedPositions[currentTab1Position] == 0
            )
                View.VISIBLE
            else
                View.GONE
    }

    private fun setupCountryFilter() {
        if (StrategyManager.isReviewPkg()) {
            binding.countryFilter.visibility = View.GONE
            return
        }

        // 观察国家筛选状态变化
        viewModel.selectedCountryCode.observe(viewLifecycleOwner) { countryCode ->
            updateCountryFilterUI(countryCode)
        }

        // 添加点击事件监听器
        binding.countryFilter.click {
            showCountryFilterDialog()
        }
    }

    private fun showCountryFilterDialog() {
        if (countryFilterPopup == null) {
            countryFilterPopup = CountryFilterPopupWindow(requireContext()) { countryCode ->
                handleCountrySelection(countryCode)
                // 弹窗消失
                countryFilterPopup?.dismiss()
            }
        }
        countryFilterPopup?.show(binding.tabLayout1)
    }

    /**
     * 处理国家筛选选择
     */
    private fun handleCountrySelection(countryCode: String?) {
        viewModel.setSelectedCountry(countryCode)

        // 只在第一个父标签(Popular)下处理国家筛选
        val tab1Index = 0
        val subTagList = viewModel.broadcasterWallTabDataLists?.get(tab1Index)?.subTagList

        // 优先用英文名匹配subTagList
        val countryEnName = countryCode?.let { CountryUtils.getCountryByIso(it)?.enName }
        val targetTab2Index = if (countryCode == null || countryCode == "ALL") {
            // 选择"All"时，切换到第一个tab2(通常是"All")
            0
        } else {
            // 查找匹配的tab2索引
            subTagList?.indexOfFirst { it.equals(countryEnName, ignoreCase = true) }
                ?.takeIf { it >= 0 } ?: 0 // 找不到时默认切换到"All"
        }

        // 切换到对应的Tab2
        val targetGlobalPosition = getGlobalPosition(tab1Index, targetTab2Index)

        // 如果当前不在目标位置，则切换
        if (viewPager.currentItem != targetGlobalPosition) {
            // 切换Tab时，ViewPager会自动创建新Fragment并加载数据
            viewPager.currentItem = targetGlobalPosition
            tab2Layout.getTabAt(targetTab2Index)?.select()
        } else {
            // 如果已经在目标位置，直接刷新当前Fragment的数据
            // 这种情况下需要显示加载动画
            refreshCurrentFragmentWithCountryFilter(countryCode)
        }
    }

    /**
     * 刷新当前Fragment的国家筛选数据
     */
    private fun refreshCurrentFragmentWithCountryFilter(countryCode: String?) {
        // 通过EventBus通知当前Fragment更新region参数并刷新数据
        EventBus.post(HomeViewModel.CountryFilterChangeEvent(countryCode))
    }

    private fun updateCountryFilterUI(countryCode: String?) {
        DrawableUtils.setRoundRectBackground(
            binding.tvHomeCountry,
            0xfff7ff38.toInt(),
            DisplayUtils.dp2pxInternal(6f).toFloat()
        )
        if (countryCode == null || countryCode == "ALL") {
            binding.tvHomeCountry.text = "All"
            binding.imageHomeCountry.setImageResource(R.drawable.map_language)
        } else {
            binding.tvHomeCountry.text = countryCode
            val iconRes = CountryUtils.getCountryIconResByIso(countryCode)
            binding.imageHomeCountry.setImageResource(iconRes)
        }
    }

    override fun onDestroyView() {
        // 清理国家筛选弹窗
        countryFilterPopup?.dismiss()
        countryFilterPopup = null

        AnimatorUtil.releaseAnimator(animationSet1)
        animationSet1 = null
        AnimatorUtil.releaseAnimator(animationSet2)
        animationSet2 = null

        PagerHelper.cleanup()

        Timber.tag("HomeFragment").d("onDestroyView: 清理资源")
        super.onDestroyView()
    }

    override fun onResume() {
        super.onResume()
        Timber.tag("HomeFragment").d("onResume")
        FlashChatManager.startShowDialogTimer()
    }

    override fun onPause() {
        Timber.tag("HomeFragment").d("onPause")
        super.onPause()
        FlashChatManager.stopShowDialogTimer()
    }

    override fun onHiddenChanged(hidden: Boolean) {
        Timber.tag("HomeFragment").d("onHiddenChanged: $hidden")
        if (hidden) {
            FlashChatManager.stopShowDialogTimer()
        } else {
            FlashChatManager.startShowDialogTimer()
        }
        super.onHiddenChanged(hidden)
    }
}