package com.score.callmetest.ui.rating

import android.app.Dialog
import android.content.Context
import android.graphics.Typeface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RatingBar
import androidx.core.net.toUri
import androidx.core.view.doOnPreDraw
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogRatingAskBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.Job

class RatingDialog(
    context: Context,
) : Dialog(context, androidx.appcompat.R.style.Theme_AppCompat_Dialog) {

    private var binding: DialogRatingAskBinding =
        DialogRatingAskBinding.inflate(LayoutInflater.from(context))

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setCancelable(false)

        // 设置全屏/居中
        window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        // 隐藏状态栏
        window?.decorView?.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
        window?.setBackgroundDrawableResource(R.color.transparent)
        // 点击空白区域可取消
        setCanceledOnTouchOutside(false)

        // 设置内容左右37dp边距
        val paddingPx = (37 * context.resources.displayMetrics.density).toInt()
        binding.root.setPadding(
            paddingPx,
            binding.root.paddingTop,
            paddingPx,
            binding.root.paddingBottom
        )

        binding.title.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));
        binding.title.text = goodText
        binding.btnText.text = "Submit"
        binding.btnLayout.doOnPreDraw {
            binding.btnLayout.background = DrawableUtils.createGradientDrawable(
                colors = GlobalManager.getMainButtonBgGradientColors(),
                radius = binding.btnLayout.height / 2f
            )
        }
        binding.btnLayout.click {
            handleSubmit()
        }

        changeState()
        binding.rbRating.onRatingBarChangeListener = object : RatingBar.OnRatingBarChangeListener {
            override fun onRatingChanged(
                ratingBar: RatingBar?,
                rating: Float,
                fromUser: Boolean
            ) {
                changeState()
            }
        }
    }

    private val goodText = context.resources.getString(R.string.rating_dialog_initial_message)
    private val badText = context.resources.getString(R.string.rating_dialog_sad_message)
    private val thankText = context.resources.getString(R.string.rating_dialog_thanks_support)

    private var badJob: Job? = null

    private fun changeState() {
        val rating = binding.rbRating.rating.toInt()
        when (rating) {
            0, 5 -> {
                binding.emoji.setImageResource(R.drawable.emoji_good)
            }

            1 -> {
                binding.emoji.setImageResource(R.drawable.emoji_cry)
            }

            2 -> {
                binding.emoji.setImageResource(R.drawable.emoji_sad)
            }

            3 -> {
                binding.emoji.setImageResource(R.drawable.emoji_smile)
            }

            4 -> {
                binding.emoji.setImageResource(R.drawable.emoji_laugh)
            }
        }
    }

    private fun handleSubmit() {
        val rating = binding.rbRating.rating.toInt()
        when (rating) {
            1, 2 -> {
                binding.title.text = badText
                binding.rbRating.visibility = View.GONE
                binding.btnText.text = "OK"
                // 这个不需要防抖
                binding.btnLayout.setOnClickListener {
                    dismiss()
                }
                badJob = ThreadUtils.runOnMainDelayed(3000L) {
                    dismiss()
                }
            }
            0, 3 -> {
                ToastUtils.showLongToast(thankText)
                dismiss()
            }
            4, 5 -> {
                ToastUtils.showLongToast(thankText)
                dismiss()
                ActivityUtils.openExternalWeb(
                    context = context,
                    h5Url = "https://play.google.com/store/apps/details?id=com.score.callme&showAllReviews=true".toUri()
                )
            }
        }
    }

    override fun dismiss() {
        badJob?.cancel()
        badJob = null
        super.dismiss()
    }
}
