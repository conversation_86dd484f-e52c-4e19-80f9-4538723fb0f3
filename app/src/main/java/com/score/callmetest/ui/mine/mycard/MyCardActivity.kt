package com.score.callmetest.ui.mine.mycard

import android.os.Bundle
import com.score.callmetest.databinding.ActivityMycardBinding
import com.score.callmetest.ui.base.BaseActivity

/**
 * 我的卡片页面的独立Activity
 * 承载MyCardFragment，避免与MainActivity的Fragment容器冲突
 */
class MyCardActivity : BaseActivity<ActivityMycardBinding, MyCardViewModel>() {

    override fun getViewBinding(): ActivityMycardBinding {
        return ActivityMycardBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = MyCardViewModel::class.java

    override fun initView() {
        // 创建MyCardFragment
        val myCardFragment = MyCardFragment()

        // 将MyCardFragment添加到Activity中
        supportFragmentManager.beginTransaction()
            .replace(binding.fragmentContainer.id, myCardFragment)
            .commit()
    }
}