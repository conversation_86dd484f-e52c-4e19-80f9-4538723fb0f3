package com.score.callmetest.manager

import com.score.callmetest.R
import com.score.callmetest.network.BroadcasterInvitationGoodsRequest
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.network.LastSpecialOfferResponse
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.PromotionGoodsRequest
import com.score.callmetest.network.QueryGoodsListRequest
import com.score.callmetest.network.SubscriptionItemResponse
import com.score.callmetest.network.SubscriptionSearchRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.SpecialOfferRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

object GoodsManager {
    private var normalGoodsCache: List<GoodsInfo>? = null
    private var promotionGoodsCache: GoodsInfo? = null
    private var subscriptionGoodsCache: List<GoodsInfo>? = null
    private var lastSpecialOfferCache: List<GoodsInfo>? = null

    private var broadcasterInvitationGoodsMapCache = mutableMapOf<String, List<GoodsInfo>>()
    private var hasFetched = false

    /** 获取普通商品列表并缓存
     * {
     * 	"code": 0,
     * 	"key": "common_success",
     * 	"msg": "成功",
     * 	"data": [{
     * 		"goodsId": "57bd1f0f-360d-46ba-a9ce-1e5ecae764a7",
     * 		"code": "263007",
     * 		"icon": "1",
     * 		"type": "0",
     * 		"subType": 0,
     * 		"thirdType": "default",
     * 		"name": "499 Coins",
     * 		"validDays": 0,
     * 		"tags": "Special Offer",
     * 		"discount": 0.60,
     * 		"originalPrice": 4.99,
     * 		"price": 1.99,
     * 		"exchangeCoin": 499,
     * 		"originalExchangeCoin": 499,
     * 		"originalPriceRupee": 500.00,
     * 		"priceRupee": 200.00,
     * 		"localPaymentPriceRupee": 188.00,
     * 		"isPromotion": true,
     * 		"isForNew": false,
     * 		"localPayOriginalPrice": 500,
     * 		"localPayPrice": 188,
     * 		"sortDesc": 0,
     * 		"extraGainCoin": 0
     * 	}, {
     * 		"goodsId": "95bf6df9-8a61-426e-b998-cb9a4bfb8a78",
     * 		"code": "263001",
     * 		"icon": "1",
     * 		"type": "0",
     * 		"subType": 0,
     * 		"thirdType": "default",
     * 		"name": "100 Coins",
     * 		"validDays": 0,
     * 		"discount": 0.00,
     * 		"originalPrice": 0.99,
     * 		"price": 0.99,
     * 		"exchangeCoin": 100,
     * 		"originalExchangeCoin": 100,
     * 		"originalPriceRupee": 100.00,
     * 		"priceRupee": 100.00,
     * 		"localPaymentPriceRupee": 94.00,
     * 		"isPromotion": false,
     * 		"isForNew": false,
     * 		"localPayOriginalPrice": 100,
     * 		"localPayPrice": 94,
     * 		"sortDesc": 0,
     * 		"extraGainCoin": 0
     * 	}, {
     * 		"goodsId": "a4c34a0a-1c3e-4d13-955e-9ec163464631",
     * 		"code": "263002",
     * 		"icon": "1",
     * 		"type": "0",
     * 		"subType": 0,
     * 		"thirdType": "default",
     * 		"name": "499 Coins",
     * 		"validDays": 0,
     * 		"discount": 0.00,
     * 		"originalPrice": 4.99,
     * 		"price": 4.99,
     * 		"exchangeCoin": 499,
     * 		"originalExchangeCoin": 499,
     * 		"originalPriceRupee": 500.00,
     * 		"priceRupee": 500.00,
     * 		"localPaymentPriceRupee": 470.00,
     * 		"isPromotion": false,
     * 		"isForNew": false,
     * 		"localPayOriginalPrice": 500,
     * 		"localPayPrice": 470,
     * 		"sortDesc": 0,
     * 		"extraGainCoin": 0
     * 	}, {
     * 		"goodsId": "9125c5ff-4862-49e9-8703-324f26ee141f",
     * 		"code": "263003",
     * 		"icon": "1",
     * 		"type": "0",
     * 		"subType": 0,
     * 		"thirdType": "default",
     * 		"name": "1200 Coins",
     * 		"validDays": 0,
     * 		"tags": "Hot",
     * 		"discount": 0.15,
     * 		"originalPrice": 9.99,
     * 		"price": 9.99,
     * 		"exchangeCoin": 1200,
     * 		"originalExchangeCoin": 1000,
     * 		"originalPriceRupee": 990.00,
     * 		"priceRupee": 990.00,
     * 		"localPaymentPriceRupee": 940.00,
     * 		"isPromotion": false,
     * 		"isForNew": false,
     * 		"localPayOriginalPrice": 990,
     * 		"localPayPrice": 940,
     * 		"sortDesc": 0,
     * 		"extraGainCoin": 0
     * 	}, {
     * 		"goodsId": "ff6caa7a-135a-4b20-8213-20a475aec2f7",
     * 		"code": "263004",
     * 		"icon": "1",
     * 		"type": "0",
     * 		"subType": 0,
     * 		"thirdType": "default",
     * 		"name": "2499 Coins",
     * 		"validDays": 0,
     * 		"discount": 0.20,
     * 		"originalPrice": 19.99,
     * 		"price": 19.99,
     * 		"exchangeCoin": 2499,
     * 		"originalExchangeCoin": 1999,
     * 		"originalPriceRupee": 1950.00,
     * 		"priceRupee": 1950.00,
     * 		"localPaymentPriceRupee": 1880.00,
     * 		"isPromotion": false,
     * 		"isForNew": false,
     * 		"localPayOriginalPrice": 1950,
     * 		"localPayPrice": 1880,
     * 		"sortDesc": 0,
     * 		"extraGainCoin": 0
     * 	}, {
     * 		"goodsId": "d2dfa43a-86fd-4ab5-9e6b-0927ba260f5f",
     * 		"code": "263005",
     * 		"icon": "1",
     * 		"type": "0",
     * 		"subType": 0,
     * 		"thirdType": "default",
     * 		"name": "5198 Coins",
     * 		"validDays": 0,
     * 		"discount": 0.25,
     * 		"originalPrice": 39.99,
     * 		"price": 39.99,
     * 		"exchangeCoin": 5198,
     * 		"originalExchangeCoin": 3999,
     * 		"originalPriceRupee": 4064.00,
     * 		"priceRupee": 4064.00,
     * 		"localPaymentPriceRupee": 4064.00,
     * 		"isPromotion": false,
     * 		"isForNew": false,
     * 		"localPayOriginalPrice": 4064,
     * 		"localPayPrice": 4064,
     * 		"sortDesc": 0,
     * 		"extraGainCoin": 0
     * 	}, {
     * 		"goodsId": "808707a4-b9fe-4a7e-9a92-2502f918dfcb",
     * 		"code": "263006",
     * 		"icon": "1",
     * 		"type": "0",
     * 		"subType": 0,
     * 		"thirdType": "default",
     * 		"name": "14999 Coins",
     * 		"validDays": 0,
     * 		"tags": "Big Deal",
     * 		"discount": 0.35,
     * 		"originalPrice": 99.99,
     * 		"price": 99.99,
     * 		"exchangeCoin": 14999,
     * 		"originalExchangeCoin": 9999,
     * 		"originalPriceRupee": 9700.00,
     * 		"priceRupee": 9700.00,
     * 		"localPaymentPriceRupee": 9400.00,
     * 		"isPromotion": false,
     * 		"isForNew": false,
     * 		"localPayOriginalPrice": 9700,
     * 		"localPayPrice": 9400,
     * 		"sortDesc": 0,
     * 		"extraGainCoin": 0
     * 	}],
     * 	"success": true,
     * 	"fail": false
     * }
     * */

    /** 获取普通商品并缓存（多个） */
    suspend fun getNormalGoods(forceRefresh: Boolean = false): List<GoodsInfo> = withContext(Dispatchers.IO) {
        if (!forceRefresh && normalGoodsCache != null) return@withContext normalGoodsCache!!
        val goodsRequest = QueryGoodsListRequest(isIncludeSubscription = false)
        val goodsResponse = RetrofitUtils.dataRepository.queryGoodsListPostV2(goodsRequest)
        val goodsList = if(goodsResponse is NetworkResult.Success){
            goodsResponse.data?.map {
                it.copy(type = "0")   //自定义商品类型
            } ?: emptyList()
        }else emptyList()
        normalGoodsCache = goodsList
        goodsList
    }

    /** 获取促销商品并缓存（单个） */
    suspend fun getPromotionGoods(forceRefresh: Boolean = false): GoodsInfo? = withContext(Dispatchers.IO) {
        if (!forceRefresh && promotionGoodsCache != null) return@withContext promotionGoodsCache
        val promoRequest = PromotionGoodsRequest(isIncludeSubscription = false)
        val promoResponse = RetrofitUtils.dataRepository.getPromotionGoods(promoRequest)
        val promoGoods = if(promoResponse is NetworkResult.Success){
            promoResponse.data?.copy(type = "1")   //自定义商品类型 1
        }else null
        promotionGoodsCache = promoGoods
        promoGoods
    }

    /** 获取订阅商品并缓存 */
    suspend fun getSubscriptionGoods(forceRefresh: Boolean = false): List<GoodsInfo> = withContext(Dispatchers.IO) {
        if (!forceRefresh && subscriptionGoodsCache != null) return@withContext subscriptionGoodsCache!!
        val subRequest = SubscriptionSearchRequest()
        val subResponse = RetrofitUtils.dataRepository.searchSubscriptions(subRequest)
        val subGoods = if(subResponse is NetworkResult.Success){
            subResponse.data ?: emptyList()
        }else emptyList()
        val subGoodsInfo = subGoods.map { it.toGoodsInfo().copy(type = "3") } // 设置为订阅商品类型
        subscriptionGoodsCache = subGoodsInfo
        subGoodsInfo
    }

    /**
     * 获得主播邀请链接的商品列表
     * @param [invitationId] 邀请id
     * @return [List<GoodsInfo>]
     */
    suspend fun getBroadcasterInvitationGoods(invitationId: String): List<GoodsInfo> = withContext(Dispatchers.IO) {
        var result = broadcasterInvitationGoodsMapCache[invitationId]
        if(!result.isNullOrEmpty()){
            return@withContext result
        }
        val request = BroadcasterInvitationGoodsRequest(invitationId = invitationId)
        val response = RetrofitUtils.dataRepository.getBroadcasterInvitationGoods(request)
        result = if(response is NetworkResult.Success){
            response.data ?: emptyList()
        }else emptyList()
        broadcasterInvitationGoodsMapCache.put(invitationId, result)
        return@withContext result
    }

    /** 获取活动商品并缓存（多个） */
    suspend fun getLastSpecialOfferV2(forceRefresh: Boolean = false): List<GoodsInfo> = withContext(Dispatchers.IO) {
        if (!forceRefresh && lastSpecialOfferCache != null) return@withContext lastSpecialOfferCache!!
        val request = SpecialOfferRequest(bizVer = null, invitationId = null, payChannel = "GP")
        val response = RetrofitUtils.dataRepository.getLastSpecialOfferV2(request)
        val specialOffers = if(response is NetworkResult.Success){
            response.data ?: emptyList()
        }else emptyList()
        val specialOffersInfo = specialOffers.map { it.toGoodsInfo().copy(type = "2") } // 设置为活动商品类型
        lastSpecialOfferCache = specialOffersInfo
        specialOffersInfo
    }

    /** 获取所有商品，合并三类缓存 */
    //todo 暂时注释订阅商品
     suspend fun getAllGoods(forceRefresh: Boolean = false): List<GoodsInfo> = withContext(Dispatchers.IO) {
        val promo = getPromotionGoods(forceRefresh)
        val normal = getNormalGoods(forceRefresh)
        val subs = getSubscriptionGoods(forceRefresh)
        val allGoods = mutableListOf<GoodsInfo>()
        val specialOffers = getLastSpecialOfferV2(forceRefresh)
        if (promo != null) allGoods.add(promo)
        allGoods.addAll(normal)
        //allGoods.addAll(subs)
        allGoods.addAll(specialOffers)
        hasFetched = true
        allGoods
    }

    fun getCachedNormalGoods(): List<GoodsInfo> = normalGoodsCache ?: emptyList()
    fun getCachedPromotionGoods(): GoodsInfo? = promotionGoodsCache
    fun getCachedSubscriptionGoods(): List<GoodsInfo> = subscriptionGoodsCache ?: emptyList()
    fun getCachedLastSpecialOfferV2(): List<GoodsInfo> = lastSpecialOfferCache ?: emptyList()

    fun getCachedInvitationGoods(invitationId: String): List<GoodsInfo> {
        return broadcasterInvitationGoodsMapCache[invitationId] ?: emptyList()
    }
    fun getCachedAllGoods(): List<GoodsInfo> {
        val allGoods = mutableListOf<GoodsInfo>()
        getCachedPromotionGoods()?.let { allGoods.add(it) }
        allGoods.addAll(getCachedNormalGoods())
       // allGoods.addAll(getCachedSubscriptionGoods())
        allGoods.addAll(getCachedLastSpecialOfferV2())
        return allGoods
    }
    suspend fun refreshAllGoods(): List<GoodsInfo> = getAllGoods(forceRefresh = true)

    fun getIconByGoodsId(goodsId: String?): Int {
        return when (goodsId) {
            "263001" -> {
                R.drawable.coins1
            }
            "263002" -> {
                R.drawable.coins2
            }
            "263003" -> {
                R.drawable.coins3
            }
            "263004" -> {
                R.drawable.coins4
            }
            "263005" -> {
                R.drawable.coins5
            }
            "263006" -> {
                R.drawable.coins6
            }
            else -> {
                R.drawable.coins2
            }
        }
    }


    /**
     * 获取当地商品价格--目前只做印度本地化
     * 获取当地商品价格
     * @param [goods] 货物
     * @param [isOldPrice] 是旧价吗？
     * @return [Pair<String, Double?>]
     */
    fun getLocaleGoodsPrice(goods: GoodsInfo, isOldPrice: Boolean = false): Pair<String, Double?> {
        val registerCountry = UserInfoManager.myUserInfo?.registerCountry
        return if ("IN".equals(registerCountry, ignoreCase = true)) {
            when (PaymentMethodManager.getDefaultPaymentMethod()) {
                PaymentMethodManager.PAY_CHANNEL_GP -> {
                    Pair("₹", if (isOldPrice) goods.originalPriceRupee else goods.priceRupee)
                }
                else -> {
                    if (PaymentMethodManager.getDefaultPaymentMethod()?.startsWith("LP", true) == true) {
                        Pair("₹", if (isOldPrice) goods.localPayOriginalPrice else goods.localPaymentPriceRupee)
                    } else {
                        Pair("$", if (isOldPrice) goods.originalPrice else goods.price)
                    }
                }
            }
        } else {
            Pair("$", if (isOldPrice) goods.originalPrice else goods.price)
        }
    }
}

private fun LastSpecialOfferResponse.toGoodsInfo(): GoodsInfo = GoodsInfo(
    code = code, // 商品编码
    icon = icon, // 图标
    type = type, // 类型
    discount = discount, // 折扣
    originalPrice = originalPrice, // 原价
    price = price, // 现价
    exchangeCoin = exchangeCoin, // 兑换的金币数
    originalExchangeCoin = originalExchangeCoin, // 原始兑换金币数
    originalPriceRupee = originalPriceRupee, // 原始卢比价格
    priceRupee = priceRupee, // 卢比价格
    localPaymentPriceRupee = localPaymentPriceRupee, // 本地支付卢比价格
    isPromotion = isPromotion, // 是否为促销商品
    extraCoinPercent = extraCoinPercent, // 额外金币百分比
    extraCoin = extraCoin, // 额外金币数量
    remainMilliseconds = remainMilliseconds, // 剩余毫秒数
    rechargeNum = rechargeNum, // 充值次数
    capableRechargeNum = capableRechargeNum, // 可充值次数
    invitationId = invitationId, // 邀请ID
    activityPic = activityPic, // 活动图片
    activitySmallPic = activitySmallPic, // 小活动图片
    activityName = activityName, // 活动名称
    thirdpartyCoinPercent = thirdpartyCoinPercent, // 第三方金币百分比
    localPayOriginalPrice = localPayOriginalPrice, // 本地支付原价
    localPayPrice = localPayPrice // 本地支付价格
)

// SubscriptionItemResponse 转 GoodsInfo
private fun SubscriptionItemResponse.toGoodsInfo(): GoodsInfo = GoodsInfo(
    capableRechargeNum = capableRechargeNum, // 可充值次数
    code = code, // 商品编码
    discount = discount, // 折扣
    exchangeCoin = exchangeCoin, // 兑换的金币数
    extraCoin = extraCoin, // 额外金币数量
    extraCoinPercent = extraCoinPercent, // 额外金币百分比
    goodsId = goodsId, // 商品ID
    icon = icon, // 图标
    invitationId = invitationId, // 邀请ID
    isPromotion = isPromotion, // 是否为促销商品
    originalCode = originalCode, // 原始编码
    originalPrice = originalPrice, // 原始价格
    originalPriceRupee = originalPriceRupee, // 原始卢比价格
    price = price, // 价格
    priceRupee = priceRupee, // 卢比价格
    rechargeNum = rechargeNum, // 充值次数
    remainMilliseconds = remainMilliseconds, // 剩余毫秒数
    surplusMillisecond = surplusMillisecond, // 剩余毫秒数（备用）
    tags = tags, // 标签
    type = type, // 类型
    validity = validity, // 有效期
    validityUnit = validityUnit // 有效期单位
)